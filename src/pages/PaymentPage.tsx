import TransactionDetails from '@/components/Payment/TransactionDetails';
import { setAddress, setIsAddressValid, setIsPolicyAgreed } from '@/reducer/paymentSlice';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import AddressForm, { AddressData } from '../components/Payment/AddressForm';
import CreditCardLogos from '../components/Payment/CreditCardLogos';
import PaymentHeader from '../components/Payment/PaymentHeader';
import PolicyLinks from '../components/Payment/PolicyLinks';
import TransactionSummary from '../components/Payment/TransactionSummary';
import PayrixPayfields from '../components/Payrix/PayrixPayfields';
import { PrimaryColor, SecondaryColor } from '../constants/Colors';

const PaymentPage = () => {
  const [searchParams] = useSearchParams();

  const [config, setConfig] = useState<{
    merchantId: string;
    apiKey: string;
    amount: number;
    description: string;
    mode: 'txn' | 'txnToken' | 'token';
    txnType: 'sale' | 'auth' | 'ecsale';
    returnUrl: string;
  } | null>(null);

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const dispatch = useDispatch();

  useEffect(() => {
    try {
      // Get secure token from URL
      const sessionToken = searchParams.get('token');

      if (!sessionToken) {
        throw new Error('Missing session token');
      }

      // Fetch payment details from server using the token
      fetch('/api/payment-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: sessionToken })
      })
        .then((response) => response.json())
        .then((data) => {
          setConfig({
            merchantId: data.merchantId,
            apiKey: data.publicKey,
            amount: data.amount,
            description: data.description || '',
            mode: data.mode || 'token',
            txnType: data.txnType || 'sale',
            returnUrl: data.returnUrl || ''
          });
        })
        .catch((err) => {
          console.log('PaymentPage: Error initializing:', err);
          // uncomment this to use test mode
          // setError('Failed to retrieve payment configuration');
          setConfig({
            merchantId: 't1_mer_6792ce792be0d0abed3a617', //dev
            // merchantId: 'p1_mer_681a3aa7efa4acbea08940e',
            apiKey: import.meta.env.VITE_PAYRIX_PUBLIC_API_KEY,
            amount: 100,
            description: 'Oversized T-Shirt',
            mode: 'txn',
            txnType: 'auth',
            returnUrl: ''
          });
        });
    } catch (err: any) {
      console.error('PaymentPage: Error initializing:', err);
      setError(err.message || 'Invalid configuration parameters');
    }
  }, [searchParams]);

  const handleSuccess = (response: any) => {
    console.log('PaymentPage: Payment successful:', response);
    setSuccess(true);

    if (config?.returnUrl) {
      const url = new URL(config.returnUrl);

      url.searchParams.append('status', 'success');
      url.searchParams.append('transaction_id', response.id || '');

      if (response.token) {
        url.searchParams.append('token', response.token.id || '');
      }

      console.log(`PaymentPage: Will redirect to ${url.toString()} in 1.5 seconds`);

      setTimeout(() => {
        window.location.href = url.toString();
      }, 1500);
    }
  };

  const handleFailure = (error: any) => {
    console.error('PaymentPage: Payment failed:', error);
    setError(error.message || 'Payment processing failed');

    if (config?.returnUrl) {
      const url = new URL(config.returnUrl);

      url.searchParams.append('status', 'failure');
      url.searchParams.append('error', error.message || 'Unknown error');

      console.log(`PaymentPage: Will redirect to ${url.toString()} in 1.5 seconds`);

      setTimeout(() => {
        window.location.href = url.toString();
      }, 1500);
    }
  };

  const handlePolicyAgreementChange = (isAgreed: boolean) => {
    dispatch(setIsPolicyAgreed(isAgreed));
  };

  const handleAddressChange = (newAddress: AddressData) => {
    dispatch(setAddress(newAddress));

    const isValid = !!(
      newAddress.addressLine1 &&
      newAddress.city &&
      newAddress.state &&
      newAddress.postalCode &&
      newAddress.country
    );
    dispatch(setIsAddressValid(isValid));
  };

  return (
    <div className="w-full h-full mx-auto px-5 py-5 overflow-auto bg-slate-50">
      <div className="w-[90%] mx-auto px-5 py-5">
        <PaymentHeader logoVariant="black" />
        {config && (
          <div className="w-full">
            <div className="bg-white rounded-xl shadow-sm transition-shadow duration-300 overflow-hidden mb-4 sm:mb-6">
              {/* top bar for style */}
              <div
                className="h-2"
                style={{
                  background: `linear-gradient(to right, ${PrimaryColor.hex}, ${SecondaryColor.hex})`
                }}
              ></div>
              <div className="p-3 sm:p-4 md:p-6">
                <h1
                  className="text-lg sm:text-xl md:text-2xl font-semibold text-center mb-4 sm:mb-6"
                  style={{ color: PrimaryColor.hex }}
                >
                  Secure Checkout
                </h1>

                {/* Error Messages */}
                {error && !success && (
                  <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border-l-4 border-red-500 animate-fadeIn">
                    <p className="flex items-center">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      {error}
                    </p>
                  </div>
                )}

                {/* Success Message */}
                {success && (
                  <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-lg border-l-4 border-green-500 animate-fadeIn">
                    <p className="flex items-center">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      Payment processed successfully!
                    </p>
                    {config?.returnUrl && (
                      <div className="flex items-center mt-3 text-sm">
                        <svg
                          className="animate-spin -ml-1 mr-2 h-4 w-4 text-green-600"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Redirecting you back to the merchant...
                      </div>
                    )}
                  </div>
                )}

                {!success && (
                  <>
                    {/* Transaction Details */}
                    <div className="mb-6">
                      <TransactionDetails amount={config.amount} description={config.description} />
                    </div>

                    {/* Main Form Section with 3 columns */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                      {/* Transaction Summary Column */}
                      <div className="order-1 lg:order-1 lg:col-span-1">
                        <div className="mb-6 overflow-hidden">
                          <TransactionSummary amount={config.amount} />
                        </div>
                        <div className="flex justify-center mb-6">
                          <div className="text-center bg-slate-50 p-4 rounded-lg border border-slate-100 w-full">
                            <p
                              className="text-lg font-semibold mb-3"
                              style={{ color: PrimaryColor.hex }}
                            >
                              Powered by
                            </p>
                            <img
                              src="/LogoFiles/png/Colorlogowithbackground.png"
                              alt="Auth Clear"
                              className="max-w-full h-auto w-64 mx-auto rounded shadow"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Address Form Column */}
                      <div className="order-2 lg:order-2 lg:col-span-1">
                        <AddressForm onChange={handleAddressChange} />
                        <div className="mt-3 sm:mt-4 overflow-hidden">
                          <PolicyLinks onAgreementChange={handlePolicyAgreementChange} />
                        </div>
                      </div>

                      {/* Payment Form Column */}
                      <div className="order-3 lg:order-3 lg:col-span-1">
                        <h2
                          className="text-lg font-medium mb-3 sm:mb-4"
                          style={{ color: PrimaryColor.hex }}
                        >
                          Payment Information
                        </h2>
                        <div className="mb-3 sm:mb-4">
                          <h3 className="text-sm font-medium text-gray-700 mb-2">
                            Accepted Payment Methods
                          </h3>
                          <CreditCardLogos />
                        </div>

                        <div className="transition-all duration-300 ease-in-out">
                          <PayrixPayfields
                            merchantId={config.merchantId}
                            apiKey={config.apiKey}
                            amount={config.amount}
                            onSuccess={handleSuccess}
                            onFailure={handleFailure}
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Footer Area */}
            <div className="text-center mt-4 mb-8">
              <div className="flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-6 text-xs text-gray-500">
                <div className="flex items-center">
                  <svg
                    className="w-4 h-4 mr-2"
                    style={{ color: PrimaryColor.hex }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    ></path>
                  </svg>
                  Secured by Auth Clear Payment Processing
                </div>
                <div className="flex items-center">
                  <svg
                    className="w-4 h-4 mr-2"
                    style={{ color: SecondaryColor.hex }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    ></path>
                  </svg>
                  SSL Encrypted Connection
                </div>
                <div className="flex items-center">
                  <svg
                    className="w-4 h-4 mr-2"
                    style={{ color: PrimaryColor.hex }}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                  24/7 Customer Support
                </div>
              </div>
            </div>
          </div>
        )}

        {!config && !error && (
          <div className="text-center py-12">
            <div
              className="animate-spin h-10 w-10 border-4 border-t-transparent rounded-full mx-auto"
              style={{
                borderColor: `${PrimaryColor.hex} transparent ${PrimaryColor.hex} ${PrimaryColor.hex}`
              }}
            ></div>
            <p className="mt-4" style={{ color: PrimaryColor.hex }}>
              Loading payment form...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentPage;
