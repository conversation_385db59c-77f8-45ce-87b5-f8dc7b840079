import { setMode } from '@/reducer/paymentSlice';
import { RootState } from '@/store/store';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

declare global {
  interface Window {
    PayFields: any;
  }
}

interface PayrixPayfieldsProps {
  merchantId: string;
  apiKey: string;
  amount: number;

  onSuccess?: (response: any) => void;
  onFailure?: (error: any) => void;
}

const PayrixPayfields = ({
  merchantId,
  apiKey,
  amount,
  onSuccess,
  onFailure
}: PayrixPayfieldsProps) => {
  const [loaded, setLoaded] = useState(false);
  const [scriptError, setScriptError] = useState<string | null>(null);
  const cardNumberRef = useRef<HTMLDivElement>(null);
  const cardNameRef = useRef<HTMLDivElement>(null);
  const cardCvvRef = useRef<HTMLDivElement>(null);
  const cardExpirationRef = useRef<HTMLDivElement>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isSaveCardChecked, setIsSaveCardChecked] = useState(false);
  const { address, isPolicyAgreed, isAddressValid, mode, txnType } = useSelector(
    (state: RootState) => state.payment
  );

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setMode(isSaveCardChecked ? 'txnToken' : 'txn'));
  }, [isSaveCardChecked, dispatch]);

  const isPaymentSubmitAllowed = () => {
    return isPolicyAgreed && isAddressValid;
  };

  const getButtonText = () => {
    if (isSubmitting) return 'Processing...';

    switch (mode) {
      case 'txn':
        return 'Pay Now';
      case 'txnToken':
        return 'Pay & Save Card';
      case 'token':
        return 'Save Card';
      default:
        return 'Pay Now';
    }
  };

  useEffect(() => {
    const script = document.createElement('script');
    script.src = `${import.meta.env.VITE_PAYRIX_PAYMENT_URL}?spa=1`;
    script.async = true;

    script.onload = () => {
      console.log('PayFields script loaded successfully');
      setLoaded(true);
    };

    script.onerror = (error) => {
      console.error('Failed to load PayFields script:', error);
      setScriptError('Failed to load payment processing script. Please try again later.');
      setIsSubmitting(false);
    };

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
      if (window.PayFields && typeof window.PayFields.unmountAll === 'function') {
        window.PayFields.unmountAll();
      }
    };
  }, []);

  useEffect(() => {
    if (!loaded || !window.PayFields) return;

    console.log('Configuring PayFields with:', { merchantId, apiKey, amount, mode, txnType });

    setTimeout(() => {
      try {
        window.PayFields.config.apiKey = apiKey;
        window.PayFields.config.merchant = merchantId;
        // window.PayFields.config.googlePay.enabled = true;
        // window.PayFields.config.googlePay.environment = 'TEST'; // Sandbox Access. Update to `PRODUCTION` or comment out.
        // window.PayFields.config.googlePay.buttonOptions.buttonColor = 'black';
        // window.PayFields.config.googlePay.buttonOptions.buttonType = 'pay';
        // window.PayFields.config.googlePay.buttonOptions.buttonSizeMode = 'fill';
        // window.PayFields.config.googlePay.buttonOptions.buttonLocale = 'en';

        //   // Configure Google Pay
        // window.PayFields.config.googlePay = {
        //   enabled: true,
        //   environment: 'TEST', // Change to 'PRODUCTION' for live environment
        //   buttonOptions: {
        //     buttonColor: 'black', // 'default' | 'black' | 'white'
        //     buttonType: 'pay', // 'long' | 'short' | 'pay' | 'plain'
        //     buttonSizeMode: 'fill', // 'static' | 'fill'
        //     buttonLocale: 'en' // Default to 'en'
        //   }
        // };

        window.PayFields.config.mode = mode;
        window.PayFields.config.txnType = txnType;

        if (mode === 'token') {
          window.PayFields.config.amount = 0;

          if (txnType !== 'auth') {
            console.warn(
              'Token-only mode typically uses auth transaction type. Current type:',
              txnType
            );
          }
        } else {
          window.PayFields.config.amount = amount;
        }

        window.PayFields.onValidationFailure = (err: any) => {
          const errorDetails = JSON.stringify(err);
          console.log('Specific validation error:', errorDetails);
          console.error('PayFields validation failed');
          setIsSubmitting(false);
          setValidationError('Payment validation failed. Please check your card details.');
          if (onFailure)
            onFailure({
              message: 'Payment validation failed. Please check your card details.',
              details: err
            });
        };

        window.PayFields.onFinish = (response: any) => {
          console.log('PayFields finished:', response);
          setIsSubmitting(false);
        };

        const fields = [
          {
            type: 'number',
            element: '#card-number'
          },
          {
            type: 'name',
            element: '#card-name'
          },
          {
            type: 'cvv',
            element: '#card-cvv'
          },
          {
            type: 'expiration',
            element: '#card-expiration'
          }
        ];

        window.PayFields.fields = fields;

        window.PayFields.customizations.style = {
          '.input': {
            display: 'block',
            width: '100%',
            padding: '0.75rem 1rem',
            fontSize: '0.875rem',
            fontWeight: '400',
            lineHeight: '1.5rem',
            backgroundColor: '#fff',
            border: '1px solid #e2e8f0',
            borderRadius: '0.375rem',
            appearance: 'none'
          },
          '.form-error': {
            color: '#e53e3e',
            fontSize: '0.75rem',
            marginTop: '0.25rem'
          }
        };

        window.PayFields.customizations.placeholders = {
          '#expiration': 'MM/YY',
          '#payment_cvv': 'CVV',
          '#payment_number': '0000 0000 0000 0000',
          '#name': 'Full Name on Card'
        };

        window.PayFields.onSuccess = (response: any) => {
          console.log('Payment successful:', response);
          setIsSubmitting(false);
          setValidationError(null);
          if (onSuccess) onSuccess(response);
        };

        window.PayFields.onFailure = (err: any) => {
          console.error('Payment failed:', err);
          setIsSubmitting(false);

          let errorMessage = 'Payment processing failed. Please try again.';
          if (err && Array.isArray(err.errors)) {
            const fieldErrors = err.errors.map((e: any) => `${e.field}: ${e.msg}`).join(', ');
            errorMessage = `Payment failed: ${fieldErrors}`;
          }

          setValidationError(errorMessage);
          if (onFailure) onFailure(err);
        };

        window.PayFields.ready();
        console.log('PayFields initialized successfully');
      } catch (error) {
        console.error('Error configuring PayFields:', error);
        setScriptError('Error configuring payment processor. Please try again later.');
        setIsSubmitting(false);
      }
    }, 500);
  }, [loaded, apiKey, merchantId, mode, txnType, amount, onSuccess, onFailure]);

  // Update only billing address when address changes
  useEffect(() => {
    if (!loaded || !window.PayFields) return;

    // Only update the billing address configuration when address changes
    if (window.PayFields.config) {
      window.PayFields.config.billingAddress = address
        ? {
            address: [address.addressLine1, address.addressLine2].filter(Boolean).join(', '),
            city: address.city,
            state: address.state,
            zip: address.postalCode,
            country: address.country
          }
        : {
            address: '',
            city: '',
            state: '',
            zip: '',
            country: '',
            phone: '',
            email: ''
          };
    }
  }, [loaded, address]);

  const handleSubmit = () => {
    if (!window.PayFields || isSubmitting || !isPaymentSubmitAllowed()) return;

    console.log('Submitting payment...');
    setIsSubmitting(true);
    setValidationError(null);

    const timeoutId = setTimeout(() => {
      if (isSubmitting) {
        console.warn('Payment submission timed out');
        setIsSubmitting(false);
        if (onFailure) onFailure({ message: 'Payment processing timed out. Please try again.' });
      }
    }, 30000);

    try {
      window.PayFields.submit();
    } catch (error) {
      console.error('Error submitting payment:', error);
      clearTimeout(timeoutId);
      setIsSubmitting(false);
      if (onFailure) onFailure({ message: 'Error processing payment. Please try again.' });
    }
  };

  if (scriptError) {
    return (
      <div className="p-4 bg-red-50 text-red-800 rounded-md">
        <p>{scriptError}</p>
      </div>
    );
  }

  return (
    <div className="payrix-payfields">
      {validationError && (
        <div className="p-4 mb-4 bg-red-50 text-red-800 rounded-md">
          <p>{validationError}</p>
        </div>
      )}

      {/* <div id="googlePayButton"></div> */}

      {/* <div className="relative mb-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">Or pay with card</span>
        </div>
      </div> */}

      <div className="mb-4">
        <label htmlFor="card-name" className="block mb-2 text-sm font-medium">
          Cardholder Name
        </label>
        <div id="card-name" ref={cardNameRef} className="h-12 border rounded-md"></div>
      </div>

      <div className="mb-4">
        <label htmlFor="card-number" className="block mb-2 text-sm font-medium">
          Card Number
        </label>
        <div id="card-number" ref={cardNumberRef} className="h-12 border rounded-md"></div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label htmlFor="card-expiration" className="block mb-2 text-sm font-medium">
            Expiration Date
          </label>
          <div
            id="card-expiration"
            ref={cardExpirationRef}
            className="h-12 border rounded-md"
          ></div>
        </div>
        <div>
          <label htmlFor="card-cvv" className="block mb-2 text-sm font-medium">
            CVV
          </label>
          <div id="card-cvv" ref={cardCvvRef} className="h-12 border rounded-md"></div>
        </div>
      </div>

      {/* Mode-specific message */}
      {mode === 'txnToken' && (
        <div className="mb-4 p-3 bg-blue-50 text-blue-700 rounded-md text-sm">
          Your card will be securely saved for future payments.
        </div>
      )}

      {/* checkbox to save card */}
      <div className="mb-4 flex items-center">
        <input
          type="checkbox"
          id="save-card"
          className="w-4 h-4 cursor-pointer"
          checked={isSaveCardChecked}
          onChange={() => setIsSaveCardChecked(!isSaveCardChecked)}
        />
        <label
          htmlFor="save-card"
          className="text-sm font-medium ml-2 cursor-pointer"
          onClick={() => {
            console.log('save card');
          }}
        >
          Save card for future payments
        </label>
      </div>

      {mode === 'token' && (
        <div className="mb-4 p-3 bg-blue-50 text-blue-700 rounded-md text-sm">
          Your card will be securely saved for future payments. No charges will be made now.
        </div>
      )}

      <button
        onClick={handleSubmit}
        disabled={isSubmitting || !isPaymentSubmitAllowed()}
        className={`w-full py-2 px-4 font-medium rounded-md ${
          isSubmitting || !isPaymentSubmitAllowed()
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {!isPaymentSubmitAllowed() ? 'Please Accept Terms to Continue' : getButtonText()}
      </button>

      {loaded && (
        <div className="mt-4 text-xs text-gray-500">
          Payment processor loaded successfully. Fill in the form and click "{getButtonText()}" to
          proceed.
        </div>
      )}

      <div className="mt-6 text-center text-xs text-gray-400">
        Secured by Auth Clear Payment Processing
      </div>
    </div>
  );
};

export default PayrixPayfields;
