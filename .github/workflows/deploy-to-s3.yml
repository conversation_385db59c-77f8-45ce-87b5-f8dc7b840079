name: Deploy to S3

on:
  push:
    branches:
      - dev
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      # - name: Set up Node.js
      #   uses: actions/setup-node@v4
      #   with:
      #     node-version: 18
      #     cache: 'npm'

      # - name: Install Dependencies
      #   run: npm ci

      # - name: Build
      #   run: npm run build
      #   env:
      #     VITE_ENV: 'dev'
      #     NODE_ENV: 'production'
      #     BASE_URL: './'

      # - name: Configure AWS Credentials
      #   uses: aws-actions/configure-aws-credentials@v4
      #   with:
      #     aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      #     aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      #     aws-region: us-east-1

      # - name: Deploy to S3
      #   run: |
      #     # First sync all files to S3
      #     aws s3 sync ./dist s3://auth-clear-ui/ --delete

      #     # Set proper cache control headers and content types
      #     # HTML files: no-cache
      #     aws s3 cp s3://auth-clear-ui/ s3://auth-clear-ui/ --recursive --metadata-directive REPLACE --cache-control max-age=0,no-cache,no-store,must-revalidate --content-type text/html --exclude "*" --include "*.html"

      #     # JS files: cache for 1 week
      #     aws s3 cp s3://auth-clear-ui/ s3://auth-clear-ui/ --recursive --metadata-directive REPLACE --cache-control max-age=604800 --content-type application/javascript --exclude "*" --include "*.js"

      #     # CSS files: cache for 1 week
      #     aws s3 cp s3://auth-clear-ui/ s3://auth-clear-ui/ --recursive --metadata-directive REPLACE --cache-control max-age=604800 --content-type text/css --exclude "*" --include "*.css"

      #     # Images and other static assets: cache for 1 month
      #     aws s3 cp s3://auth-clear-ui/ s3://auth-clear-ui/ --recursive --metadata-directive REPLACE --cache-control max-age=2592000 --exclude "*" --include "*.jpg" --include "*.jpeg" --include "*.png" --include "*.gif" --include "*.svg" --include "*.ico"

      #     # Create CORS configuration
      #     cat > cors-config.json << EOL
      #     {
      #       "CORSRules": [
      #         {
      #           "AllowedOrigins": ["*"],
      #           "AllowedHeaders": ["*"],
      #           "AllowedMethods": ["GET", "HEAD", "PUT", "POST", "DELETE"],
      #           "MaxAgeSeconds": 3000
      #         }
      #       ]
      #     }
      #     EOL

      #     # Apply CORS configuration
      #     aws s3api put-bucket-cors --bucket auth-clear-ui --cors-configuration file://cors-config.json

      #     echo "Deployment completed! Website available at: http://auth-clear-ui.s3-website-us-east-1.amazonaws.com"
