### STEPS TO FOLLOW
1. First think through the problem, read the codebase for relevant files, and write a plan to tasks/todo.md.
2. The plan should have a list of todo items that you can check off as you complete them
3. Before you begin working, check in with me and I will verify the plan.
4. Then, begin working on the todo items, marking them as complete as you go.
5. Please every step of the way just give me a high level explanation of what changes you made
6. Make every task and code change you do as simple as possible. We want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.
7. Finally, add a review section to the [todo.md] file with a summary of the changes you made and any other relevant information. 

### ROLE AND RULES
Act as a senior software developer.
Please reply in a concise style. Avoid unnecessary repetition or filler language.
Don't add any comments unless absolutely necessary
Refactor files >250 lines.
Always prefer simple, modular solutions.
Avoid code duplication, Check for existing utilities/functions before adding new code.
Only change what's requested or clearly related.
When fixing bugs, Exhaust all options with the current stack before introducing new tech.
Remove deprecated code if new patterns are introduced.
Keep the codebase clean, organized, and consistent.
Avoid one-off scripts in main codebase.
Never overwrite .env without explicit confirmation.
Prioritize reusability and robust error handling.
Use modern standards, tools, and patterns.
Work step-by-step; break down complex tasks.
Design for reusability and flexibility.
Minimize dependencies; keep it lightweight.
Ensure mobile and desktop responsiveness.
Functions must be small, single-purpose (SRP).
Handle all errors and edge cases.
Use try/catch for async/await.
Write self-explanatory code.

## Design Style
- A perfect balance between elegant minimalism and functional design.
- Soft, refreshing gradient colors that seamlessly integrate with the brand palette.
- Well-proportioned white space for a clean layout.
- Light and immersive user experience.
- Clear information hierarchy using subtle shadows and modular card layouts.
- Natural focus on core functionalities.
- Refined rounded corners.
- Delicate micro-interactions.
- Comfortable visual proportions.
- Accent colors chosen based on the app type.


# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server with Vite
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run lint` - Run ESLint with auto-fix enabled
- `npm run preview` - Preview production build locally

### Testing
No test framework is currently configured in this project.

## Project Architecture

### Tech Stack
- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: TailwindCSS with custom Metronic theme system
- **UI Components**: Combination of custom components and shadcn/ui (Radix UI primitives)
- **State Management**: Redux Toolkit + Redux Persist
- **Routing**: React Router v6
- **Forms**: Formik + Yup validation
- **Payment Integration**: Payrix payment fields

### Key Directory Structure
- `src/auth/` - Authentication system with JWT support, includes login/signup/2FA flows
- `src/components/` - Reusable UI components including Payment, Payrix, and core UI primitives
- `src/pages/` - Main application pages including payment processing and onboarding
- `src/layouts/` - Layout components for different app sections (auth, demo1, errors)
- `src/routing/` - Route configuration and setup
- `src/providers/` - React context providers for settings, menus, translations
- `src/plugins/` - TailwindCSS plugins for component styling
- `src/reducer/` - Redux slices for state management

### Authentication Architecture
- JWT-based authentication with branded and classic layout options
- Multi-step flows: login → 2FA → password reset sequences
- Auth routes are nested under `/auth/*` with automatic layout wrapping
- Protected routes use `RequireAuth` wrapper component

### Payment System Architecture
- Payrix integration for secure payment processing
- Payment page accepts session tokens via URL parameters for security
- Supports multiple transaction modes: txn, txnToken, token
- Transaction types: sale, auth, ecsale
- Address validation and policy agreement tracking via Redux

### Theme System
- Dual theme support (light/dark) managed via settings provider
- Custom color system with contextual variants (primary, success, danger, etc.)
- Metronic design system with elegant minimalism and functional design principles
- CSS custom properties for dynamic theming

### Layout System
- Multiple layout types: Demo1 (sidebar + header), Auth (branded/classic), Errors
- Responsive sidebar with collapse functionality
- Configurable layouts with provider pattern

## Important Notes

### Payrix Integration
- Always check https://resource.payrix.com/resources/ before making Payrix-related changes
- Use the @Payrix-guide documentation when working with payment features

### Design Principles (from .cursor/rules)
- Elegant minimalism with functional design
- Soft gradient colors integrated with brand palette
- Well-proportioned white space and clean layouts
- Light, immersive user experience with subtle shadows
- Natural focus on core functionalities with refined rounded corners

### Task Management
- Create markdown task files (TASKS.md or feature-specific) for complex implementations
- Structure with Completed/In Progress/Future Tasks sections
- Include implementation plans for features

### File Aliases
- `@/` resolves to `src/` directory for cleaner imports

### Code Style
- TypeScript strict mode enabled
- ESLint configuration with Prettier integration
- Component-first architecture with reusable primitives
- Prefer absolute imports using `@/` alias
